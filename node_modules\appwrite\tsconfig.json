{
    "compilerOptions": {
        "allowJs": true,
        "allowSyntheticDefaultImports": true,
        "baseUrl": "src",
        "declaration": false,
        "esModuleInterop": true,
        "inlineSourceMap": false,
        "lib": ["ESNext", "DOM"],
        "listEmittedFiles": false,
        "listFiles": false,
        "moduleResolution": "node",
        "noFallthroughCasesInSwitch": true,
        "pretty": true,
        "rootDir": "src",
        "skipLibCheck": true,
        "strict": true,
        "target": "ES6",
        "traceResolution": false,
    },
    "compileOnSave": false,
    "exclude": ["node_modules", "dist"],
    "include": ["src"]
}