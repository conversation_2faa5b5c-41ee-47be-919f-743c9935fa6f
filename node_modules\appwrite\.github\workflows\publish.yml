name: Publish to NPM

on:
  release:
    types: [published]
  workflow_dispatch:

jobs:
  publish:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    # Setup Node.js environment
    - name: Use Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        registry-url: 'https://registry.npmjs.org'

    # Determine release tag based on the tag name
    - name: Determine release tag
      id: release_tag
      run: |
        if [[ "${{ github.ref }}" == *"-rc"* ]] || [[ "${{ github.ref }}" == *"-RC"* ]]; then
          echo "tag=next" >> "$GITHUB_OUTPUT"
        else
          echo "tag=latest" >> "$GITHUB_OUTPUT"
        fi

    # Install dependencies (if any) and build your project (if necessary)
    - name: Install dependencies and build
      run: |
        npm install
        npm run build

    # Publish to NPM with the appropriate tag
    - name: Publish
      run: npm publish --tag ${{ steps.release_tag.outputs.tag }}
      env:
        NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
