import { Client, Account } from "appwrite";

const client = new Client()
    .setEndpoint('https://<REGION>.cloud.appwrite.io/v1') // Your API Endpoint
    .setProject('<YOUR_PROJECT_ID>'); // Your project ID

const account = new Account(client);

const result = await account.create(
    '<USER_ID>', // userId
    '<EMAIL>', // email
    '', // password
    '<NAME>' // name (optional)
);

console.log(result);
