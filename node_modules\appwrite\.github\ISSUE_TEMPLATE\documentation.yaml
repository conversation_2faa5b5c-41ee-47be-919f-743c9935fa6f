name: "📚 Documentation"
description: "Report an issue related to documentation"
title: "📚 Documentation: "
labels: [documentation]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to make our documentation better 🙏
  - type: textarea
    id: issue-description
    validations:
      required: true
    attributes:
      label: "💭 Description"
      description: "A clear and concise description of what the issue is."
      placeholder: "Documentation should not ..."
  - type: checkboxes
    id: no-duplicate-issues
    attributes:
      label: "👀 Have you spent some time to check if this issue has been raised before?"
      description: "Have you Googled for a similar issue or checked our older issues for a similar bug?"
      options:
        - label: "I checked and didn't find similar issue"
          required: true
  - type: checkboxes
    id: read-code-of-conduct
    attributes:
      label: "🏢 Have you read the Code of Conduct?"
      options:
        - label: "I have read the [Code of Conduct](https://github.com/appwrite/appwrite/blob/HEAD/CODE_OF_CONDUCT.md)"
          required: true