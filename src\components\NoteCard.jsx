import { useEffect, useRef, useState } from "react";
import { db } from "../appwrite/database";
import Trash from "../icons/Trash";
import { autoGrow, bodyParser, setNewOffset, setZIndex } from "../utils";

const NoteCard = ({ note }) => {
  let mouseStartPos = { x: 0, y: 0 };

  const colors = JSON.parse(note.colors);
  const body = bodyParser(note.body);

  const [position, setPositon] = useState(JSON.parse(note.position));

  const cardRef = useRef(null);
  const textAreaRef = useRef(null);

  useEffect(() => {
    autoGrow(textAreaRef);
  }, []);

  const mouseDown = (e) => {
    setZIndex(cardRef.current);

    mouseStartPos.x = e.clientX;
    mouseStartPos.y = e.clientY;

    document.addEventListener("mousemove", mouseMove);
    document.addEventListener("mouseup", mouseUp);
  };

  const mouseMove = (e) => {
    //1 - Calculate move direction
    const moveDirection = {
      x: mouseStartPos.x - e.clientX,
      y: mouseStartPos.y - e.clientY,
    };

    //2 - Update start position for next move.
    mouseStartPos.x = e.clientX;
    mouseStartPos.y = e.clientY;

    //3 - Update card top and left position.

    const newPosition = setNewOffset(cardRef.current, moveDirection);
    setPositon(newPosition);
  };

  const mouseUp = () => {
    document.removeEventListener("mousemove", mouseMove);
    document.removeEventListener("mouseup", mouseUp);

    const newPosition = setNewOffset(cardRef.current);
    saveData("position", newPosition);
    saveData("zIndex", Number(cardRef.current.style.zIndex));
  };

  const handleTextareaMouseDown = (e) => {
    // Prevent the card's mouseDown event from firing when clicking on textarea
    e.stopPropagation();
  };

  const saveData = async (key, value) => {
    // Handle zIndex as plain integer, other values as JSON strings
    const payload = { [key]: key === "zIndex" ? value : JSON.stringify(value) };
    try {
      await db.notes.update(note.$id, payload);
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <div
      className="card"
      ref={cardRef}
      style={{
        backgroundColor: colors.colorBody,
        top: `${position.y}px`,
        left: `${position.x}px`,
        zIndex: note.zIndex,
        color: colors.colorText,
      }}
      onMouseDown={mouseDown}
    >
      <div
        className="card-header"
        style={{ backgroundColor: colors.colorHeader }}
      >
        <Trash />
      </div>
      <div className="card-body">
        <textarea
          ref={textAreaRef}
          style={{ color: colors.colorText }}
          defaultValue={body}
          onInput={() => autoGrow(textAreaRef)}
          onFocus={() => setZIndex(cardRef.current)}
          onMouseDown={handleTextareaMouseDown}
        ></textarea>
      </div>
    </div>
  );
};

export default NoteCard;
