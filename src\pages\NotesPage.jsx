import { useEffect, useState } from "react";
import { db } from "../appwrite/database";
import NoteCard from "../components/NoteCard";

const NotesPage = () => {
  const [notes, setNotes] = useState([]);

  const init = async() => {
    const response = await db.notes.list();
    setNotes(response.documents);
  };

  useEffect(() => {
    init();
  }, []);

  return (
    <div>
      {notes.map((note) => (
        <NoteCard key={note.$id} note={note} />
      ))}
    </div>
  );
};

export default NotesPage;
